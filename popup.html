<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline'">
  <title>OG-proxy 登录</title>
  <style>
    body {
      width: 300px;
      font-family: Arial, sans-serif;
      text-align: center;
      padding: 20px;
      background-color: #ffffff;
    }

    .logo {
      width: 80px;
      height: 80px;
      margin: 0 auto 10px auto;
      border-radius: 50%;
      background-image: url("icons/icon128.png");
      background-size: cover;
      background-position: center;
    }

    .tip {
      color: #f39c12;
      font-size: 13px;
      margin: 6px 0 10px;
    }

    .input-container {
      display: flex;
      border: 1px solid #ccc;
      border-radius: 5px;
      overflow: hidden;
      margin-bottom: 10px;
    }

    .input-container input {
      border: none;
      padding: 10px;
      flex: 1;
      outline: none;
      font-size: 14px;
    }

    .input-container button {
      background-color: #f0f0f0;
      border: none;
      padding: 10px 15px;
      cursor: pointer;
      font-size: 13px;
    }

    .btn-confirm {
      width: 100%;
      background-color: #2980b9;
      color: white;
      font-weight: bold;
      padding: 12px;
      font-size: 15px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      margin-top: 12px;
    }

    .advantage {
      text-align: left;
      font-size: 12px;
      color: #333;
      margin-top: 15px;
    }

    .status {
      margin-top: 10px;
      font-size: 13px;
      color: green;
    }

    .btn-exit {
      width: 100%;
      background-color: #e74c3c;
      color: white;
      font-weight: bold;
      padding: 12px;
      font-size: 15px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      margin-top: 12px;
    }

    .btn-exit:hover {
      background-color: #c0392b;
    }
    .connect{
      border-radius: 50%;
      width: 50px;
      height: 50px;
      display: flex;
      flex-direction: column;
    }
  </style>
</head>
<body>
<div id="step1">
  <div class="logo"></div>
  <div class="tip">请输入授权码才能使用！</div>

  <div class="input-container">
    <input id="token" type="text" placeholder="请输入授权码" value="">
    <button id="pasteBtn">粘贴</button>
  </div>
</div>
<!--<div id="step12">-->
<!--  <div class="connect">-->
<!--    <svg t="1754365108504" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1521" width="64" height="64"><path d="M511.1 923.8C295 923.8 119.2 748 119.2 531.9c0-108.8 45.8-213.6 125.7-287.6 24-22.2 61.4-20.8 83.6 3.2 22.2 24 20.8 61.4-3.2 83.6-55.8 51.6-87.7 124.8-87.7 200.8 0 150.9 122.7 273.6 273.6 273.6s273.6-122.7 273.6-273.6c0-76.7-30.9-147.8-87-200.1-23.9-22.3-25.2-59.7-2.9-83.6 22.3-23.9 59.7-25.2 83.6-2.9C857.6 319.1 903 423.6 903 531.9c0 216.1-175.8 391.9-391.9 391.9z" fill="#52A9FF" p-id="1522"></path><path d="M516.7 663.3c-32.5 0-59.2-26.6-59.2-59.2V160.5c0-32.5 26.6-59.2 59.2-59.2 32.5 0 59.2 26.6 59.2 59.2v443.7c-0.1 32.5-26.7 59.1-59.2 59.1z" fill="#00EFEF" p-id="1523"></path></svg>-->
<!--  </div>-->
<!--  <div class="tip">请输入授权码才能使用！</div>-->

<!--  <div class="input-container">-->
<!--    <input id="token" type="text" placeholder="请输入授权码" value="">-->
<!--    <button id="pasteBtn">粘贴</button>-->
<!--  </div>-->
<!--</div>-->



<button id="loginBtn" class="btn-confirm">确 定</button>
<button id="exitBtn" class="btn-exit" style="display: none;">退出登录</button>


<div class="advantage">
  <b>三大优势</b><br>
  1. 完全免费、免维护、无广告。<br>
  2. 专线直连，速度快。<br>
  3. 全加密传输，防止数据被抓取。
</div>

<div class="status" id="status">当前状态：未登录</div>

<script src="popup.js"></script>

</body>
</html>

