document.addEventListener('DOMContentLoaded', () => {
    const codeInput = document.getElementById("token");
    const loginBtn = document.getElementById("loginBtn");
    const exitBtn = document.getElementById("exitBtn");
    const statusText = document.getElementById("status");

    function updateStatus() {
        chrome.storage.local.get(["proxyEnabled"], (data) => {
            if (data && data.proxyEnabled) {
                exitBtn.style.display = 'block';
                loginBtn.style.display = 'none';
                statusText.textContent = "代理状态：已启用"
            } else {
                exitBtn.style.display = 'none';
                loginBtn.style.display = 'block';
                statusText.textContent = "代理状态：未启用";
            }
        });
    }

    function parseAddress(address) {
        const url = new URL(address);
        return {
            host: url.hostname,
            port: url.port || (url.protocol === 'https:' ? 1080 : 1080)
        };
    }

    loginBtn.onclick = () => {
        const token = codeInput.value.trim();
        if (!token) {
            alert('请输入授权码');
            return;
        }
        fetch('https://vpn-api.ogweb.org/api/auth/verify', {
            method: 'POST',
            body: JSON.stringify({
                token: token,
                region: 'CN'
            }),
            credentials: 'include'
        })
            .then(res => res.json())
            .then(({data}) => {
                if (data.nodes && data.nodes.length > 0) {
                    const node = data.nodes[0];
                    const {host, port} = parseAddress(node.address);
                    const proxy = {
                        host: host,
                        port: port,
                        protocol: "socks5"
                    };
                    chrome.storage.local.set({proxyConfig: proxy, domain: data.domain,token}, () => {
                        chrome.runtime.sendMessage({
                            type: "connect"
                        }, (res) => {
                            if (res.success) {
                                alert('登录成功，域名分流代理已启用');
                            } else {
                                alert(res.message || "授权失败");
                            }
                        });
                    });
                } else {
                    alert("授权码验证失败，未返回有效节点");
                }
            }).catch(error => {
            console.error("登录请求失败:", error);
            alert("请求失败，请检查网络");
        });

    };

    function close(){
        chrome.runtime.sendMessage({type: "close"}, (res) => {
            alert("已关闭代理")
            updateStatus();
        });
    }

    exitBtn.onclick = () => {
        close()
    };

    updateStatus();


    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === "status-update") {
            updateStatus();
        }
        if (message.type === "status-close"){
            close()
        }
    });

});




