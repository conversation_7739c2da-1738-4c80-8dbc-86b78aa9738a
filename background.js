let timmer = null
let token = null
function generatePacScript(data) {
  console.log(data)
  const conditions = data.domain
    .map(d => `shExpMatch(host, "*.${d}") || shExpMatch(host, "${d}")`)
    .join(" || ");
  return `
    function FindProxyForURL(url, host) {
      if (${conditions}) {
        return "SOCKS5 ${data.proxyConfig.host}:${data.proxyConfig.port}";
      }
      return "DIRECT";
    }
  `;
}

function setProxy(enable,data) {
  if (enable) {
    const pacScript = generatePacScript(data)
    chrome.proxy.settings.set({
      value: {
        mode: "pac_script",
        pacScript: {
          data: pacScript
        }
      },
      scope: "regular"
    }, () =>{
      console.log("代理已启用")
      heartbeat()
      chrome.storage.local.set({ proxyEnabled: true });
      chrome.runtime.sendMessage({ type: "status-update", payload: "代理已启动" });
    });
  } else {
    chrome.proxy.settings.set({
      value: {
        mode: "direct"
      },
      scope: "regular"
    }, () => {
      console.log("代理已关闭")
      stopHeartbeat()
      chrome.storage.local.set({ proxyEnabled: false });
    });
  }
}

chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
  if (msg.type === "connect") {
      chrome.storage.local.get(["proxyConfig",'domain','token'], (data) => {
        token = data.token
        setProxy(true,data)
      });
      sendResponse({ success: true });
  } else if (msg.type === "close") {
    setProxy(false);
    chrome.storage.local.set({ proxyEnabled: false });
    sendResponse({ success: true });
  } else if (msg.type === "status") {
    chrome.storage.local.get(["proxyEnabled"], (data) => {
      if(data){
        sendResponse({ enabled: false });
      }else{
        sendResponse({ enabled: data.proxyEnabled });
      }
    });
  }
});

function checkProxyStatus() {
  fetch('https://vpn-api.ogweb.org/api/auth/ping', {
    method: 'POST',
    body: JSON.stringify({
      token: token,
    }),
    credentials: 'include'
  }).then((res) => res.json())
      .then((res) => {
        console.log("检测 IP：", res);
        const {data} = res;
        if(data.block_proxy){
          chrome.runtime.sendMessage({ type: "status-close", payload: "status error" });
        }
      })
      .catch((e) => {
        chrome.runtime.sendMessage({ type: "status-close", payload: "status error" });
      });
}


function heartbeat() {
  timmer = setInterval(checkProxyStatus, 5 * 1000);
}

function stopHeartbeat() {
  clearInterval(timmer);
  timmer = null;
}


chrome.runtime.onStartup.addListener(() => {
  console.log('OG-proxy startup, starting heartbeat');
  chrome.storage.local.get(["proxyEnabled"], (data) => {
    if (data.proxyEnabled) {
      heartbeat()
    }
  });

});


chrome.action.onClicked.addListener((tab) => {
  console.log('Extension icon clicked');
  // This keeps the service worker alive
});
